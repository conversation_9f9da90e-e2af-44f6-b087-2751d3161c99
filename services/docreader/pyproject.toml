[project]
name = "docreader"
version = "0.1.0"
description = ""
readme = "README.md"
requires-python = ">=3.12,<4.0"
dependencies = [
    "paddlepaddle (>=3.0.0,<4.0.0)",
    "paddleocr (>=2.10.0,<3.0.0)",
    "playwright (>=1.51.0,<2.0.0)",
    "setuptools (>=79.0.0,<80.0.0)",
    "textract (>=1.6.5,<2.0.0)",
    "antiword (>=0.3.2,<0.4.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
