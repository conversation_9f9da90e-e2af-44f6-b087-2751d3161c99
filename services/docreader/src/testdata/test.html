<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试 HTML 文档</title>
</head>
<body>
    <h1>测试 HTML 文档</h1>
    
    <p>这是一个测试 HTML 文档，用于测试 HTML 解析功能。</p>
    
    <h2>包含图片</h2>
    <img src="https://example.com/image.jpg" alt="测试图片">
    
    <h2>包含链接</h2>
    <p>这是一个<a href="https://example.com">测试链接</a>。</p>
    
    <h2>包含代码块</h2>
    <pre><code>
def hello_world():
    print("Hello, World!")
    </code></pre>
    
    <h2>包含表格</h2>
    <table>
        <thead>
            <tr>
                <th>表头1</th>
                <th>表头2</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>内容1</td>
                <td>内容2</td>
            </tr>
            <tr>
                <td>内容3</td>
                <td>内容4</td>
            </tr>
        </tbody>
    </table>
    
    <h2>测试分块功能</h2>
    <p>这部分内容用于测试分块功能，确保 HTML 结构在分块时保持完整。</p>
    <ul>
        <li>第一块内容</li>
        <li>第二块内容</li>
        <li>第三块内容</li>
    </ul>
    
    <h2>测试重叠功能</h2>
    <p>这部分内容可能会在分块时与前后块重叠，以确保上下文的连续性。</p>
</body>
</html> 