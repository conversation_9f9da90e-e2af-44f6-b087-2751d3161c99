// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: docreader.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DocReader_ReadFromFile_FullMethodName = "/docreader.DocReader/ReadFromFile"
	DocReader_ReadFromURL_FullMethodName  = "/docreader.DocReader/ReadFromURL"
)

// DocReaderClient is the client API for DocReader service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 文档读取服务
type DocReaderClient interface {
	// 从文件读取文档
	ReadFromFile(ctx context.Context, in *ReadFromFileRequest, opts ...grpc.CallOption) (*ReadResponse, error)
	// 从URL读取文档
	ReadFromURL(ctx context.Context, in *ReadFromURLRequest, opts ...grpc.CallOption) (*ReadResponse, error)
}

type docReaderClient struct {
	cc grpc.ClientConnInterface
}

func NewDocReaderClient(cc grpc.ClientConnInterface) DocReaderClient {
	return &docReaderClient{cc}
}

func (c *docReaderClient) ReadFromFile(ctx context.Context, in *ReadFromFileRequest, opts ...grpc.CallOption) (*ReadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReadResponse)
	err := c.cc.Invoke(ctx, DocReader_ReadFromFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *docReaderClient) ReadFromURL(ctx context.Context, in *ReadFromURLRequest, opts ...grpc.CallOption) (*ReadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReadResponse)
	err := c.cc.Invoke(ctx, DocReader_ReadFromURL_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DocReaderServer is the server API for DocReader service.
// All implementations must embed UnimplementedDocReaderServer
// for forward compatibility.
//
// 文档读取服务
type DocReaderServer interface {
	// 从文件读取文档
	ReadFromFile(context.Context, *ReadFromFileRequest) (*ReadResponse, error)
	// 从URL读取文档
	ReadFromURL(context.Context, *ReadFromURLRequest) (*ReadResponse, error)
	mustEmbedUnimplementedDocReaderServer()
}

// UnimplementedDocReaderServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDocReaderServer struct{}

func (UnimplementedDocReaderServer) ReadFromFile(context.Context, *ReadFromFileRequest) (*ReadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadFromFile not implemented")
}
func (UnimplementedDocReaderServer) ReadFromURL(context.Context, *ReadFromURLRequest) (*ReadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadFromURL not implemented")
}
func (UnimplementedDocReaderServer) mustEmbedUnimplementedDocReaderServer() {}
func (UnimplementedDocReaderServer) testEmbeddedByValue()                   {}

// UnsafeDocReaderServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DocReaderServer will
// result in compilation errors.
type UnsafeDocReaderServer interface {
	mustEmbedUnimplementedDocReaderServer()
}

func RegisterDocReaderServer(s grpc.ServiceRegistrar, srv DocReaderServer) {
	// If the following call pancis, it indicates UnimplementedDocReaderServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DocReader_ServiceDesc, srv)
}

func _DocReader_ReadFromFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadFromFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocReaderServer).ReadFromFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DocReader_ReadFromFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocReaderServer).ReadFromFile(ctx, req.(*ReadFromFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DocReader_ReadFromURL_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadFromURLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocReaderServer).ReadFromURL(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DocReader_ReadFromURL_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocReaderServer).ReadFromURL(ctx, req.(*ReadFromURLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DocReader_ServiceDesc is the grpc.ServiceDesc for DocReader service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DocReader_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "docreader.DocReader",
	HandlerType: (*DocReaderServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ReadFromFile",
			Handler:    _DocReader_ReadFromFile_Handler,
		},
		{
			MethodName: "ReadFromURL",
			Handler:    _DocReader_ReadFromURL_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "docreader.proto",
}
