{"name": "knowledage-base", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-with-types": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "axios": "^1.8.4", "marked": "^5.1.2", "pagefind": "^1.1.1", "pinia": "^3.0.1", "tdesign-vue-next": "^1.11.5", "vue": "^3.5.13", "vue-router": "^4.5.0", "webpack": "^5.94.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/marked": "^5.0.2", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "6.0.0", "@vitejs/plugin-vue-jsx": "5.0.1", "@vue/tsconfig": "^0.7.0", "less": "^4.3.0", "less-loader": "^12.2.0", "npm-run-all2": "^7.0.2", "typescript": "~5.8.0", "vite": "7.0.4", "vue-tsc": "^2.2.8"}, "overrides": {"lightningcss": "none", "esbuild": "^0.25.0"}, "resolutions": {"lightningcss": "none", "esbuild": "^0.25.0"}}